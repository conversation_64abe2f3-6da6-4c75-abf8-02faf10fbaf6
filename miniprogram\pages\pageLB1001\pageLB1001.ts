// pages/pageBL1001/pageBL1001.ts
import { IAppOption } from "../../../typings/index"
import { BleDataCallback, BleDataHandler } from "../../lib/ble-data-handler"
import { BluetoothManager } from "../../lib/bluetoothManager"
import toast from "../LB1001Setting/toast"
import { initBLTChange, sendData, disConnectSoBack, hexToDec,decimalToHexadecimal, reConnection, ab2hex,asciiToHex } from "../LB1001Setting/index"
import AnalyzeData from "../LB1001Setting/AnalyzeData"

const app = getApp<IAppOption>()
var sBluetoothManager: BluetoothManager
Page({

  /**
   * 页面的初始数据
   */
  data: {
    doorShow: true,
    openValue:'开门',
    silderValue1:1,
    silderValue2:2,
    checked:false,
    lbstatus:0,//0代表关门 1代表开门
    lblong:0,
    isStudy:true,//是否在学习模式，如果不在，设备也不回复则判定为掉线
    isShowToast: false, //是否显示弹窗
    toastSuccess: true, //弹窗的类型 //1为蓝，0为红
    toastText: '操作成功', //弹窗文字
    arrlist:[],
    types:false,
    timp:true,
    isShowPwd:false,//设置密码弹窗
    isHead:true,//弹窗头部
    isInput:true,//弹窗密码
    isValidation:false,//校验密码弹窗
    inputValue: '',
    passWord:'HiLink',//HiLink
    isFastPwd:true,//第一次进来校验密码
    Version:'1.0',
    showBubble:false,//展示气泡弹窗
    openDoor_left:'left: -24.5%;',
    openDoor_right: 'left: 24.5%;',
    distanceCM:0,//实时距离
  },
  async openAndClose_door(e) {
    this.setData({
        doorShow:true
      })
    const status = e.currentTarget.dataset.status
    if (status === '1') {
      if (this.data.openValue ==='开门') {
        toast.info('当前已开门')
        return
      }
      let data="FFCC0600BBAA";
      //console.log("手动开门指令:",data);
      await sendData(data);
      this.checkKaiguan();
      this.setData({
        openDoor_left:'left:-24.5%;',
        openDoor_right:'left:24.5%;',
        openValue:'开门'
      })
    }else{
      if (this.data.openValue ==='关门') {
        toast.info('当前已关门')
        return
      }
      let data="FFCC0E00BBAA";
      await sendData(data);
      this.checkKaiguan();
      this.setData({
        openDoor_left:'left:0;',
        openDoor_right:'left:0;',
        openValue:'关门'
      })
    }
  },
  handleWenHao(e){
    console.log(e);
    const showBubble = !this.data.showBubble
    this.setData({
      showBubble
    })
  },
  onInput(e:any) {
      this.setData({
        inputValue:e.detail.value
      });
  },
  onInputow(e:any){
    this.setData({
      passWord: e.detail.value
    });
  },
  /**打开提示窗 */
  openToast(type = 1,text = '操作成功'){
    if (type ==1 ) {
      this.setData({
        toastText:text,
        toastSuccess:true,
        isShowToast:true})
        setTimeout(()=>{
          this.setData({
           isShowToast:false
          });
        },2000)
    }else{
      this.setData({
        toastText:text,
        toastSuccess:false,
        isShowToast:true})
    }
  },

  async Adjustjl() {
      this.setData({
        doorShow: false,
        types:true
      })
      // this.data.types=true;
      if(this.data.types){
        wx.showLoading({title:'智能调距中..',mask:true});
        setTimeout(()=>{
          this.setData({
            types:false
          })
          console.log("距离数组：",this.data.arrlist);
          if(this.data.arrlist.length>0){
            this.bindMain();
            // this.openToast(1,'设置成功')
            toast.info('设置成功')
            wx.hideLoading();
            this.checkjl();
            this.setData({
              arrlist:[]
            })
          }else{
            // this.openToast(1,'设置成功')
            toast.info('设置成功')
            wx.hideLoading();
          }
        },5000)
      }
      
  },
  calculateAverageWithoutMinMax(arr:Array<object>) {
    // 对数组进行排序
    arr.sort((a, b) => a - b);
    // 去掉一个最大值和一个最小值
    let newArr = arr.slice(1, arr.length - 1);
    // 计算剩余元素的总和
    let sum = newArr.reduce((a, b) => a + b, 0);
    // 计算平均值
    let average = sum / newArr.length;
    //console.log("平均值：",average);
    let cm =( average / 100).toFixed(2);
    console.log("666",cm);
    this.setData({
      distanceCM: cm
    })
    return average;
  },
     /**关闭提示窗 */
     async closeToast(){  
      this.setData({isShowToast:false})
      return true
    },
     /**关闭提示窗 */
     async closePwd(){  
       wx.setStorageSync("isFastPwd",false);
      this.setData({isShowPwd:false})
      return true
    },
    // 修改密码
    async editPwd(){
      if(this.data.isHead){
        this.setData({isHead:false,isInput:false})
      }else{
        let pwd=this.data.inputValue;
        if (pwd.length != 6) {
          wx.showToast({
            title: '请输入6位数的密码',
            icon: 'none',
          });
        }else{
          let password = asciiToHex(this.data.inputValue);
          let data = "FFCC0B00"+password+"BBAA" //FFCC01000CBBAA
          await sendData(data);
          wx.setStorageSync("PassWord",this.data.inputValue)
          this.setData({isShowPwd:false})
        }
      }
      wx.setStorageSync("isFastPwd",false);
    },
    // 取消校验密码
    async closeVld(){
      wx.navigateBack({ delta: 1 })
    },
    // 校验密码
    async validaPwd(){
      let password = asciiToHex(this.data.passWord);
      let data = "FFCC0C00"+password+"BBAA" 
      await sendData(data); 
      wx.setStorageSync("PassWord",this.data.passWord)
      await this.checkVersion();
      await this.checkOpendown();
      await this.checkjl()
      await this.checkTime()
      await this.checkKaiguan()
    },
async bindMain(){
    var num= this.calculateAverageWithoutMinMax(this.data.arrlist);
    let result = Math.floor(num / 70);
    if(result<=0){
      let data = "FFCC010001BBAA";
      await sendData(data);
    }else if(result>15){
      let results = decimalToHexadecimal(15);
      let data = "FFCC0100"+results+"BBAA";
      await sendData(data);
    }else{
      let results = decimalToHexadecimal(result);
      let data = "FFCC0100"+results+"BBAA";
      await sendData(data);
    }
},

 /** 滑动-感应范围档位 */
 sliderchanging: function(e:any){ //滑块的取值
  let value:number = e.detail.value
  if(value>15){
    value=15;
  }
  if (value === 0) {
    this.setData({
      silderValue1:1
    })
  }else{
    this.setData({
      silderValue1:value
    })
  }
},

/**设置-感应范围 */
async setInduction(_distance=0) {
  let distance = decimalToHexadecimal(_distance);
  let data = "FFCC0100"+distance+"BBAA" //FFCC01000CBBAA
  //console.log("感应范围指令:",data);
  await sendData(data);
},

/** 设置-感应范围档位 */
bindchange:function (e:any) {
  let value:number = e.detail.value<1?1:e.detail.value //最小只能为1
  this.setData({
    silderValue1:value,
  })
  this.setInduction(value);
},

/** 滑动-检测无人关门时间 */
sliderchanging2: function(e:any){ //滑块的取值
  let value:number = e.detail.value
  if (value === 0) {
    this.setData({
      silderValue2:1
    })
  }else{
    this.setData({
      silderValue2:value
    })
  }
},
 /**设置-无人关门时间 */
 async setNobodyKeeptime(_time=0) {
  let time = decimalToHexadecimal(_time);
  let data = "FFCC0200"+time+"BBAA"
  //console.log("无人关门指令:",data);
  await sendData(data)
},
/** 设置-检测无人关门时间 */
bindchange2:function (e:any) {
  let value:number = e.detail.value<1?1:e.detail.value //最小只能为1
  console.log(value);
  this.setData({
    silderValue2:value,
  })
  this.setNobodyKeeptime(value)
},

/**设置-自动开门按钮 */
async setOpenDoor(_switch=0) {
  let switchs = decimalToHexadecimal(_switch);
  let data = "FFCC0900"+switchs+"BBAA"
  //console.log("自动开门指令:",data);
  await sendData(data);
},

// 切换按钮
changeChecked(e:any){
  const checked = e.detail.value
  console.log(checked);
  this.setData({
    checked
  })
  if(checked){
    this.setOpenDoor(1);
  }else{
    this.setOpenDoor(0);
  }
},

async connectOpen(){
  let data="";
  if(this.data.openValue=='开门'){
    data = "FFCC0E00BBAA" //关门
  }else{
    data = "FFCC0600BBAA" //开门
    this.setData({openValue:'开门'})
  }
  //console.log("手动开门指令:",data);
  await sendData(data);
  this.checkKaiguan();
},

/** 查看版本 */
async checkVersion(){
  let data = "FFCC0D00BBAA".replace(/\s/g, '');
  await sendData(data);
},

/** 查看当前开关门状态 */
async checkOpendown(){
  let data = "FFCC0F00BBAA".replace(/\s/g, '');
  await sendData(data);
},

 /** 查看感觉距离 */
  async checkjl() {
  let data = "FFCC0700BBAA".replace(/\s/g, '');
  await sendData(data);
},
/** 查看无人持续时间 */
async checkTime(){
  let data = "FFCC0800BBAA".replace(/\s/g, '');
  await sendData(data)
},
/** 查看是否打开自动开门 */
async checkKaiguan(){
  let data = "FFCC0A00BBAA".replace(/\s/g, '');
  await sendData(data);
},
/** 校验密码 */
async checkPwd(){
  let pasd=wx.getStorageSync("PassWord");
  if(pasd==null||pasd==''){
    wx.setStorageSync("PassWord",this.data.passWord)
  }
  let pwd=wx.getStorageSync("PassWord");
  let password = asciiToHex(pwd);
  let data = "FFCC0C00"+password+"BBAA" 
  await sendData(data);
  await this.checkVersion();
  await this.checkOpendown();
  await this.checkjl()
  await this.checkTime()
  await this.checkKaiguan()
},
/*** 是否断开连接了，断开则回连 */
isConnected(){
  var res = sBluetoothManager.isConnected()
  console.log('设备连接状态',res);
  if (res === false) { //重新连接
    toast.info('设备断开连接，正在准备回连')
    this.reConnectionFn(1) //自动重连设备
  }
},
/** 执行设备回连逻辑 */
async reConnectionFn(count:number){
  if (sBluetoothManager.getConnectedDevice()?.deviceId != undefined 
  || getCurrentPages()[getCurrentPages().length -1].route.match(/LB1001Setting/) == null) {
     return;
  }
  console.log('执行次数',count);
  wx.showLoading({title:'正在连接'+count+'次',mask:true})
  const res:any =  await reConnection() 
    if (res === true) {
      wx.hideLoading();
      toast.info('设备已重连成功')
      return this.onShow();
    }else if ( res === false && count === 1) {
      return await this.reConnectionFn(2)
    }else if ( res === false && count === 2) {
      return await this.reConnectionFn(3)
    }else {
        disConnectSoBack('重连失败，请手动连接')
        return;
    }
},
toPageSetting(){
  wx.navigateTo({url:'../LB1001Firmare/LB1001Firmare'})
},
//点击返回
returnSetp: function () {
  wx.navigateBack({ delta: 1 })
},
// 修改密码
updatePwd() {
  this.setData({
    isShowPwd:true,
  })
  },

/**
 * 从蓝牙16进制数据中自动提取距离值并转换为米单位（2位小数）
 * @param {string} hexData - 蓝牙上报的16进制字符串（如 "4F4E0D0A52616E67652031310D0A"）
 * @returns {number|null} 转换后的米制数值（如 0.11），失败返回null
 */
 getDistance(hexData) {
  try {
    // 1. 验证输入
    if (typeof hexData !== 'string' || !/^[0-9A-Fa-f]+$/.test(hexData)) {
      console.error('错误：输入的hexData必须是纯16进制字符串');
      return null;
    }

    // 2. 查找距离数字部分（假设格式为...Range XX...）
    // 匹配模式：52 61 6E 67 65 20（"Range "的16进制）后跟数字部分
    const rangePattern = /52616E676520([0-9A-F]{2,})0D0A/;
    const match = hexData.match(rangePattern);

    if (!match) {
      console.error('错误：未找到有效的距离数据格式');
      return null;
    }

    // 3. 提取数字部分（如3131→"11"）
    const numberHex = match[1];
    const numberStr = numberHex.match(/.{2}/g)
      .map(pair => String.fromCharCode(parseInt(pair, 16)))
      .join('');

    // 4. 验证是否为纯数字
    if (!/^\d+$/.test(numberStr)) {
      console.error('错误：提取到的不是有效数字');
      return null;
    }

    // 5. 转换为米并保留2位小数
    const cmValue = parseInt(numberStr, 10);
    return parseFloat((cmValue / 100).toFixed(2));

  } catch (e) {
    console.error('转换过程中发生错误：', e);
    return null;
  }
},
LisentResponseFn(){
  var that = this;
  var LisentResponse:BleDataCallback = {
    async onReceiveData(res: WechatMiniprogram.OnBLECharacteristicValueChangeCallbackResult) {
        var _data =  ab2hex(res.value);
        let data =  _data.toUpperCase()  //将数据-转换-成大写
        var res1:any = await AnalyzeData.checkData(data) //这里会返回对象 -成功或失败 及指令内容
        console.log("设备参数：",data);
          if (res1.status == 'ok') {//数据上报处理
            if(res1.lbstatus==1){
              if(that.data.types){
                  let newArrlist = that.data.arrlist;
                  newArrlist.push(parseInt(res1.lblong));
                  that.setData({
                      arrlist:newArrlist
                  })
                }
            }
          }else{//下发指令接收数据
              console.log("下发指令接收数据",res1.str);
              if(res1.str.slice(4,6)=="07"){//读取感应距离
                var silderValue1=hexToDec(res1.str.slice(8,10));
                that.setData({
                  silderValue1:silderValue1,
                })
              }else if(res1.str.slice(4,6)=="08"){//读取无人持续时间
                var silderValue2=hexToDec(res1.str.slice(8,10));
                that.setData({
                  silderValue2:silderValue2,
                })
              }else if(res1.str.slice(4,6)=="0A"){//8.读取自动开门使能状态
                var checked=res1.str.slice(8,10)=="01"?true:false;
                that.setData({
                  checked:checked,
                })
              }else if(res1.str.slice(4,6)=="0D"){//8.读取版本  FFBB0D000104BBAA
                var Version=res1.str.slice(8,12);
                var version1=parseInt(Version.slice(0,2),10)+'.'+parseInt(Version.slice(2,4),10);
                wx.setStorageSync("Version",version1.toString());
                that.setData({
                  Version:version1.toString(),
                })
              }else if(res1.str.slice(4,6)=="0F"){//8.读取当前开关门状态
                var openvalue=res1.str.slice(8,10)=="01"?'开门':'关门';
                if (openvalue === '开门') {
                  that.setData({
                    openDoor_left:'left:-24.5%;',
                    openDoor_right:'left:24.5%;',
                    openValue:'开门'
                  })
                } else {
                  that.setData({
                    openDoor_left:'left:0;',
                    openDoor_right:'left:0;',
                    openValue:'关门',
                  })
                }
              }else if(res1.str =="FFBB0106BBAA"){//FFBB0106BBAA
                // that.openToast(1,'设置成功')
                toast.info('开门成功')
              }else if(res1.str.slice(4,6)=="01"){//下发指令成功
                // that.openToast(1,'设置成功')
                toast.info('设置成功')
              }else if(res1.str.slice(4,6)=="00"){//下发指令失败
                // that.openToast(2,'设置失败')
                toast.info('设置失败')
              }else if(res1.str.slice(4,6)=="0B"){//设置密码
                  if(res1.str.slice(6,8)=="01"){
                    that.setData({
                      isShowPwd:false
                    })
                    // that.openToast(1,'设置成功')
                    toast.info('设置成功')
                    
                  }else{
                    wx.showToast({
                      title: '设置失败',
                      icon: 'none',
                      duration: 2000,
                    })
                  }
              }else if(res1.str.slice(4,6)=="0C"){//校验密码
                if(res1.str.slice(6,8)=="01"){
                    wx.showToast({
                      title: '校验成功',
                      icon: 'none',
                      duration: 2000,
                    })
                    that.setData({
                      isValidation:false
                    })
                    // let isFastPwd=wx.getStorageSync("isFastPwd");
                    // if(isFastPwd){
                    //   let pwd=wx.getStorageSync("PassWord");
                    //   if(pwd=='HiLink'){
                    //       that.setData({
                    //         isShowPwd:true,
                    //       })
                    //   }
                    // }
                }else{
                  wx.showToast({
                    title: '校验失败',
                    icon: 'none',
                    duration: 2000,
                  })
                  that.setData({
                    isValidation:true,
                  })
                }
              }else if(res1.str.slice(4,6)=="0E"){//设置手动关门
                if(res1.str.slice(6,8)=="00"){
                  // that.openToast(1,'设置成功')
                  toast.info('关门成功')
                  that.setData({
                    openValue:'关门'
                  });
                }else{
                  toast.info('设置失败')
                  // that.openToast(2,'设置失败')
                }
              }
          }
        }
      }
    return LisentResponse
},
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

    sBluetoothManager = app.globalData.bluetoothManager
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    wx.setStorageSync('isShow',true)
    const isFastPwd = wx.getStorageSync('isFastPwd');
    if (isFastPwd && !wx.getStorageSync('hasShownPwdTip')) {
      wx.showModal({
        title:'设置/修改密码',
        content:"请修改蓝牙密码，默认密码下任何人都可以使用小程序手动开关门和设置参数。修改密码后，只有密码正确才能修改参数和手动开关",
        showCancel:false, 
        success:()=>{
          wx.setStorageSync('hasShownPwdTip', true);
        }
      })
    }
    //wx.showLoading({title:'读取数据中..',mask:true})
    if (BleDataHandler.callbacks.length < 2) {
      BleDataHandler.addCallbacks(this.LisentResponseFn())
    }
    let device:any = sBluetoothManager.getConnectedDevice();
    console.log('连接状态：',device);
    if (device === null) { //如果
      return await this.reConnectionFn(1)
    }
    wx.setStorageSync("Version", this.Version);
    const res = await initBLTChange()
    console.log('初始化结果：', res);
    await this.checkPwd()
    wx.hideLoading()
    if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageLB1001/) != null) {
      this.setData({isStudy:false})
      wx.setStorageSync("showToast",true) 
    }

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('小程序从前台进入后台时触发');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
    // 可以在这里进行清理工作，如暂停音频、清除定时器等  
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.setData({isStudy:true})
    wx.setStorageSync("showToast",false) 
    console.log('跳转了');
    if (BleDataHandler.callbacks[1] != undefined) {
      console.log('移除监听');
      BleDataHandler.removeCallbacks(BleDataHandler.callbacks[1])
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})